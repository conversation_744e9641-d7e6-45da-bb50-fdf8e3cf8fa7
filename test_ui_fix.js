/**
 * 关卡选择页面UI修复测试脚本
 * 用于验证UI元素重叠问题是否得到解决
 */

// 模拟不同屏幕尺寸的测试数据
const testScreenSizes = [
  { width: 375, height: 667, name: 'iPhone SE' },
  { width: 414, height: 896, name: 'iPhone 11 Pro Max' },
  { width: 360, height: 640, name: 'Android Small' },
  { width: 480, height: 854, name: 'Android Medium' }
]

/**
 * 测试UI元素位置和重叠检测
 */
function testUILayout() {
  console.log('🧪 开始测试关卡选择页面UI布局...\n')
  
  testScreenSizes.forEach(screen => {
    console.log(`📱 测试屏幕: ${screen.name} (${screen.width}x${screen.height})`)
    
    const centerX = screen.width / 2
    
    // 修复后的UI元素位置
    const titleBarHeight = 140
    const titleTextY = 55
    const backButtonX = 60
    const backButtonY = 95
    const backButtonWidth = 110
    const backButtonHeight = 40
    const progressInfoY = 115
    const levelGridStartY = 170
    
    // 计算UI元素边界
    const titleTextBounds = {
      left: centerX - 100, // 估算文字宽度的一半
      right: centerX + 100,
      top: titleTextY - 19, // 字体大小38px的一半
      bottom: titleTextY + 19
    }
    
    const backButtonBounds = {
      left: backButtonX - backButtonWidth / 2,
      right: backButtonX + backButtonWidth / 2,
      top: backButtonY - backButtonHeight / 2,
      bottom: backButtonY + backButtonHeight / 2
    }
    
    const progressInfoBounds = {
      left: centerX - 150, // 进度信息宽度300px的一半
      right: centerX + 150,
      top: progressInfoY - 15, // 高度30px的一半
      bottom: progressInfoY + 15
    }
    
    // 检测重叠
    let hasOverlap = false
    let issues = []
    
    // 检查标题文字与返回按钮是否重叠
    if (isOverlapping(titleTextBounds, backButtonBounds)) {
      hasOverlap = true
      issues.push('❌ 标题文字与返回按钮重叠')
    } else {
      issues.push('✅ 标题文字与返回按钮无重叠')
    }
    
    // 检查返回按钮与进度信息是否重叠
    if (isOverlapping(backButtonBounds, progressInfoBounds)) {
      hasOverlap = true
      issues.push('❌ 返回按钮与进度信息重叠')
    } else {
      issues.push('✅ 返回按钮与进度信息无重叠')
    }
    
    // 检查标题栏高度是否足够
    if (titleBarHeight < Math.max(titleTextY + 19, backButtonY + backButtonHeight/2, progressInfoY + 15)) {
      hasOverlap = true
      issues.push('❌ 标题栏高度不足')
    } else {
      issues.push('✅ 标题栏高度充足')
    }
    
    // 检查刘海屏适配（标题文字是否在安全区域）
    const safeAreaTop = 44 // iPhone刘海屏安全区域
    if (titleTextY - 19 < safeAreaTop) {
      issues.push('⚠️  标题文字可能被刘海屏遮挡')
    } else {
      issues.push('✅ 标题文字在安全区域内')
    }
    
    // 输出测试结果
    issues.forEach(issue => console.log(`  ${issue}`))
    
    if (hasOverlap) {
      console.log(`  🔴 ${screen.name}: 存在UI重叠问题`)
    } else {
      console.log(`  🟢 ${screen.name}: UI布局正常`)
    }
    
    console.log(`  📏 关键位置:`)
    console.log(`    - 标题栏高度: ${titleBarHeight}px`)
    console.log(`    - 标题文字Y: ${titleTextY}px`)
    console.log(`    - 返回按钮: (${backButtonX}, ${backButtonY})px`)
    console.log(`    - 进度信息Y: ${progressInfoY}px`)
    console.log(`    - 关卡网格起始Y: ${levelGridStartY}px`)
    console.log('')
  })
  
  console.log('🎯 测试完成！')
}

/**
 * 检测两个矩形区域是否重叠
 */
function isOverlapping(rect1, rect2) {
  return !(rect1.right < rect2.left || 
           rect2.right < rect1.left || 
           rect1.bottom < rect2.top || 
           rect2.bottom < rect1.top)
}

/**
 * 生成触摸区域测试数据
 */
function generateTouchTests() {
  console.log('🎯 生成触摸区域测试数据...\n')
  
  const testPoints = [
    { x: 60, y: 95, expected: 'back_button', desc: '返回按钮中心' },
    { x: 5, y: 75, expected: 'back_button', desc: '返回按钮左上角' },
    { x: 115, y: 115, expected: 'back_button', desc: '返回按钮右下角' },
    { x: 200, y: 55, expected: 'title', desc: '标题文字区域' },
    { x: 200, y: 115, expected: 'progress', desc: '进度信息区域' },
    { x: 100, y: 200, expected: 'level_grid', desc: '关卡网格区域' }
  ]
  
  console.log('测试点击区域:')
  testPoints.forEach(point => {
    console.log(`  (${point.x}, ${point.y}) - ${point.desc} -> 期望: ${point.expected}`)
  })
  
  return testPoints
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testUILayout, generateTouchTests }
} else {
  testUILayout()
  generateTouchTests()
}
