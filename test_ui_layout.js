/**
 * 游戏UI布局测试脚本
 * 验证修复后的UI元素位置是否正确
 */

// 模拟游戏主界面的布局参数
const gameUILayout = {
  // 屏幕尺寸（模拟手机屏幕）
  screenWidth: 400,
  screenHeight: 600,
  
  // 修复后的布局参数
  topBar: {
    height: 100,  // 从70px扩大到100px
    gradient: {
      start: 'rgba(74, 144, 226, 0.9)',
      middle: 'rgba(80, 227, 194, 0.8)',
      end: 'rgba(184, 233, 134, 0.7)'
    }
  },
  
  // UI元素位置
  uiElements: {
    movesInfo: {
      x: 25,
      y: 50,  // 从35px调整到50px
      width: 80,
      height: 30
    },
    
    scoreInfo: {
      x: 200,  // 居中
      y: 55,   // barHeight/2 + 5 = 100/2 + 5 = 55px
      width: 120,
      height: 35
    },
    
    levelInfo: {
      x: 375,  // 右上角
      y: 50,   // 从35px调整到50px
      width: 100,
      height: 30
    }
  },
  
  // 游戏板位置
  gameBoard: {
    x: 20,
    y: 110,  // 从80px调整到110px
    width: 360,  // screenWidth - 40
    height: 300  // screenHeight * 0.5
  },
  
  // 槽位位置
  slot: {
    x: 15,
    y: 445,  // screenHeight - 80 - 75 = 600 - 155 = 445
    width: 370,  // screenWidth - 30
    height: 80
  }
};

/**
 * 验证UI布局是否合理
 */
function validateUILayout() {
  console.log('🎮 游戏UI布局验证开始...\n');
  
  // 验证顶部标签栏
  console.log('📊 顶部标签栏验证：');
  console.log(`  高度: ${gameUILayout.topBar.height}px (修复前: 70px)`);
  console.log(`  ✅ 标签栏高度已扩大，为UI元素提供更多空间\n`);
  
  // 验证UI元素位置
  console.log('🎯 UI元素位置验证：');
  
  const { movesInfo, scoreInfo, levelInfo } = gameUILayout.uiElements;
  
  console.log(`  移动次数信息: (${movesInfo.x}, ${movesInfo.y}) - 修复前: (25, 35)`);
  console.log(`  分数信息: (${scoreInfo.x}, ${scoreInfo.y}) - 修复前: (200, 50)`);
  console.log(`  关卡信息: (${levelInfo.x}, ${levelInfo.y}) - 修复前: (375, 35)`);
  
  // 检查是否有足够的间距避免刘海屏遮挡
  const minSafeY = 40;  // 刘海屏安全区域
  const allElementsY = [movesInfo.y, scoreInfo.y, levelInfo.y];
  const minY = Math.min(...allElementsY);
  
  if (minY >= minSafeY) {
    console.log(`  ✅ 所有UI元素Y坐标 >= ${minSafeY}px，避免了刘海屏遮挡\n`);
  } else {
    console.log(`  ❌ 部分UI元素Y坐标 < ${minSafeY}px，可能被刘海屏遮挡\n`);
  }
  
  // 验证游戏板位置
  console.log('🎲 游戏板位置验证：');
  const { gameBoard } = gameUILayout;
  console.log(`  位置: (${gameBoard.x}, ${gameBoard.y}) - 修复前: (20, 80)`);
  console.log(`  尺寸: ${gameBoard.width}x${gameBoard.height}`);
  
  // 检查游戏板是否与顶部标签栏有足够间距
  const boardTopMargin = gameBoard.y - gameUILayout.topBar.height;
  if (boardTopMargin >= 10) {
    console.log(`  ✅ 游戏板与标签栏间距: ${boardTopMargin}px，布局合理\n`);
  } else {
    console.log(`  ❌ 游戏板与标签栏间距: ${boardTopMargin}px，间距不足\n`);
  }
  
  // 验证槽位位置
  console.log('📦 槽位位置验证：');
  const { slot } = gameUILayout;
  console.log(`  位置: (${slot.x}, ${slot.y})`);
  console.log(`  尺寸: ${slot.width}x${slot.height}`);
  
  const slotBottomMargin = gameUILayout.screenHeight - (slot.y + slot.height);
  console.log(`  底部边距: ${slotBottomMargin}px\n`);
  
  // 总体布局验证
  console.log('📐 总体布局验证：');
  const totalUsedHeight = gameUILayout.topBar.height + 
                         (gameBoard.y - gameUILayout.topBar.height) + 
                         gameBoard.height + 
                         (slot.y - (gameBoard.y + gameBoard.height)) + 
                         slot.height + 
                         slotBottomMargin;
  
  console.log(`  屏幕高度: ${gameUILayout.screenHeight}px`);
  console.log(`  已使用高度: ${totalUsedHeight}px`);
  
  if (totalUsedHeight <= gameUILayout.screenHeight) {
    console.log(`  ✅ 布局合理，所有元素都在屏幕范围内`);
  } else {
    console.log(`  ❌ 布局超出屏幕范围`);
  }
  
  console.log('\n🎉 UI布局验证完成！');
}

/**
 * 显示修复前后的对比
 */
function showLayoutComparison() {
  console.log('\n📊 修复前后对比：');
  console.log('┌─────────────────┬──────────┬──────────┐');
  console.log('│ 项目            │ 修复前   │ 修复后   │');
  console.log('├─────────────────┼──────────┼──────────┤');
  console.log('│ 标签栏高度      │ 70px     │ 100px    │');
  console.log('│ 游戏板Y坐标     │ 80px     │ 110px    │');
  console.log('│ 关卡信息Y坐标   │ 35px     │ 50px     │');
  console.log('│ 移动次数Y坐标   │ 35px     │ 50px     │');
  console.log('│ 分数信息Y坐标   │ 50px     │ 55px     │');
  console.log('└─────────────────┴──────────┴──────────┘');
  
  console.log('\n✨ 修复效果：');
  console.log('• 扩大了顶部标签栏，提供更好的视觉层次');
  console.log('• 所有UI元素往下移动，避免与刘海屏重叠');
  console.log('• 保持了各元素之间的合理间距');
  console.log('• 游戏板和槽位位置相应调整，整体布局协调');
}

// 运行验证
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { validateUILayout, showLayoutComparison, gameUILayout };
} else {
  // 浏览器环境
  validateUILayout();
  showLayoutComparison();
}

// 如果在Node.js环境中直接运行
if (typeof require !== 'undefined' && require.main === module) {
  validateUILayout();
  showLayoutComparison();
}
